# CleanTrackGH

CleanTrackGH is a React Native application designed to help users manage and track cleaning tasks, collections, and analytics. The app features authentication, task management, collection creation, and analytics visualization, providing a modern and user-friendly experience.

## Features

- **Authentication**: Secure login and user session management.
- **Task Management**: Create, view, and manage cleaning tasks.
- **Collections**: Organize tasks into collections for better tracking.
- **Analytics**: Visualize cleaning progress and statistics.
- **Modern UI**: Built with reusable components for a clean and intuitive interface.

## Project Structure

```
app.json                # Expo/React Native app configuration
package.json            # Project dependencies and scripts
tsconfig.json           # TypeScript configuration
app/                    # Main app screens and navigation
  _layout.tsx           # App layout
  +not-found.tsx        # 404 screen
  index.tsx             # Home screen
  login.tsx             # Login screen
  modal.tsx             # Modal screen
  (tabs)/               # Tab navigation screens
    _layout.tsx         # Tabs layout
    analytics.tsx       # Analytics tab
    index.tsx           # Main tab
    map.tsx             # Map tab
    settings.tsx        # Settings tab
  collection/           # Collection-related screens
    new.tsx             # New collection screen
  task/                 # Task-related screens
    [id].tsx            # Task detail screen
components/             # Reusable UI components
  Button.tsx
  Card.tsx
  Input.tsx
  StatCard.tsx
  TaskCard.tsx
hooks/                  # Custom React hooks
  auth-store.ts         # Authentication state management
  collection-store.ts   # Collection state management
  task-store.ts         # Task state management
mocks/                  # Mock data for development
  analytics.ts
  collections.ts
  tasks.ts
  users.ts
types/                  # TypeScript type definitions
  index.ts
```

## Getting Started

### Prerequisites
- [Node.js](https://nodejs.org/) (v16 or later recommended)
- [Yarn](https://yarnpkg.com/) or [npm](https://www.npmjs.com/)
- [Expo CLI](https://docs.expo.dev/get-started/installation/) (for running the app)

### Installation

1. **Clone the repository:**
   ```sh
   git clone <repository-url>
   cd cleantrackgh
   ```
2. **Install dependencies:**
   ```sh
   yarn install
   # or
   npm install
   ```
3. **Start the development server:**
   ```sh
   yarn start
   # or
   npm start
   ```
4. **Run on your device or emulator:**
   - Use the Expo Go app on your phone, or
   - Run on an Android/iOS simulator from the Expo Dev Tools

## Scripts

- `yarn start` / `npm start` — Start the Expo development server
- `yarn android` / `npm run android` — Run on Android emulator/device
- `yarn ios` / `npm run ios` — Run on iOS simulator/device
- `yarn web` / `npm run web` — Run in web browser

## Technologies Used

- **React Native**
- **Expo**
- **TypeScript**
- **React Navigation**

## Folder Details

- **app/**: Contains all screens and navigation logic.
- **components/**: Reusable UI components for consistency and maintainability.
- **hooks/**: Custom hooks for state management and business logic.
- **mocks/**: Mock data for development and testing.
- **types/**: TypeScript type definitions for strong typing.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/YourFeature`)
3. Commit your changes (`git commit -am 'Add some feature'`)
4. Push to the branch (`git push origin feature/YourFeature`)
5. Open a pull request

## License

This project is licensed under the MIT License.

## Contact

For questions or support, please open an issue in the repository.
