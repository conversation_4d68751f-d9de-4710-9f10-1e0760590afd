# Supabase Edge Functions Setup for Paystack Payments

This guide will help you set up Supabase Edge Functions to handle your Paystack payments securely.

## Prerequisites

1. A Supabase account and project
2. Supabase CLI installed
3. Your Paystack secret key

## Step 1: Install Supabase CLI

```bash
npm install -g supabase
```

## Step 2: Login to Supabase

```bash
supabase login
```

## Step 3: Link Your Project

```bash
supabase link --project-ref your-project-id
```

## Step 4: Set Environment Variables

Set your Paystack secret key as a secret in Supabase:

```bash
supabase secrets set PAYSTACK_SECRET_KEY=sk_test_your_actual_secret_key_here
```

## Step 5: Deploy the Functions

Deploy both payment functions:

```bash
supabase functions deploy initialize-payment
supabase functions deploy verify-payment
```

## Step 6: Update Your React Native App

In your `services/payment.ts` file, update these values:

```typescript
const SUPABASE_URL = 'https://your-project-id.supabase.co'
const SUPABASE_ANON_KEY = 'your-supabase-anon-key'
```

You can find these values in your Supabase Dashboard:
- Go to Settings → API
- Copy the Project URL and anon/public key

## Step 7: Test the Functions

You can test the functions using curl or your React Native app:

### Test Initialize Payment
```bash
curl -X POST 'https://your-project-id.supabase.co/functions/v1/initialize-payment' \
  -H 'Authorization: Bearer your-supabase-anon-key' \
  -H 'Content-Type: application/json' \
  -d '{
    "amount": 10,
    "phoneNumber": "233501234567",
    "email": "<EMAIL>",
    "reference": "TEST_123456",
    "paymentMethod": "mtn_momo",
    "customerName": "John Doe"
  }'
```

### Test Verify Payment
```bash
curl -X GET 'https://your-project-id.supabase.co/functions/v1/verify-payment/TEST_123456' \
  -H 'Authorization: Bearer your-supabase-anon-key' \
  -H 'Content-Type: application/json'
```

## Security Notes

1. **Never expose your Paystack secret key** in client-side code
2. The secret key is stored securely in Supabase and only accessible by your Edge Functions
3. Your React Native app only needs the Supabase anon key, which is safe to expose
4. All payment processing happens server-side in the Edge Functions

## Troubleshooting

### Function not found error
- Make sure you've deployed the functions: `supabase functions deploy`
- Check the function names match exactly

### 401 Unauthorized
- Verify your Supabase anon key is correct
- Check that the functions are deployed and accessible

### Paystack API errors
- Verify your Paystack secret key is set correctly: `supabase secrets list`
- Make sure you're using the correct key for your environment (test vs live)

### CORS errors
- The functions include CORS headers, but make sure you're calling from the correct domain
- Check that the `corsHeaders` are properly imported and used

## Next Steps

1. Replace the placeholder values in your `services/payment.ts`
2. Test the payment flow in your React Native app
3. Monitor function logs: `supabase functions logs`
4. Set up proper error handling and user feedback in your app
