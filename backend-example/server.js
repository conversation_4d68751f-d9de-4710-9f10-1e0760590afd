// Example backend server for handling Paystack payments
// This is a Node.js/Express server that securely handles Paystack API calls

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch'); // or use built-in fetch in Node.js 18+

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Environment variables - NEVER expose these in client-side code
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY; // sk_test_... or sk_live_...
const PAYSTACK_BASE_URL = 'https://api.paystack.co';

// Validate environment variables
if (!PAYSTACK_SECRET_KEY) {
  console.error('PAYSTACK_SECRET_KEY environment variable is required');
  process.exit(1);
}

// Helper functions
function getPaystackChannel(paymentMethod) {
  switch (paymentMethod) {
    case 'mtn_momo':
    case 'vodafone_cash':
    case 'airteltigo_money':
      return 'mobile_money';
    default:
      return 'mobile_money';
  }
}

function getPaystackProvider(paymentMethod) {
  switch (paymentMethod) {
    case 'mtn_momo':
      return 'mtn';
    case 'vodafone_cash':
      return 'vod';
    case 'airteltigo_money':
      return 'tgo';
    default:
      return 'mtn';
  }
}

// Initialize payment endpoint
app.post('/api/payments/initialize', async (req, res) => {
  try {
    const { amount, phoneNumber, email, reference, paymentMethod, customerName } = req.body;

    // Validate required fields
    if (!amount || !phoneNumber || !email || !reference || !paymentMethod || !customerName) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Call Paystack API with secret key
    const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/initialize`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: amount * 100, // Convert to pesewas (smallest unit)
        email,
        reference,
        currency: 'GHS',
        channels: [getPaystackChannel(paymentMethod)],
        metadata: {
          phone_number: phoneNumber,
          customer_name: customerName,
          payment_method: paymentMethod,
        },
        mobile_money: {
          phone: phoneNumber,
          provider: getPaystackProvider(paymentMethod),
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return res.status(response.status).json({
        success: false,
        message: errorData.message || 'Payment initialization failed',
        error: errorData
      });
    }

    const data = await response.json();
    res.json(data);

  } catch (error) {
    console.error('Payment initialization error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Verify payment endpoint
app.get('/api/payments/verify/:reference', async (req, res) => {
  try {
    const { reference } = req.params;

    if (!reference) {
      return res.status(400).json({
        success: false,
        message: 'Payment reference is required'
      });
    }

    // Call Paystack API with secret key
    const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/verify/${reference}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return res.status(response.status).json({
        success: false,
        message: errorData.message || 'Payment verification failed',
        error: errorData
      });
    }

    const data = await response.json();
    res.json(data);

  } catch (error) {
    console.error('Payment verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`Payment server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});
