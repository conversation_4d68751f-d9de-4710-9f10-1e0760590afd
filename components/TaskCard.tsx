import React from 'react'
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native'
import { useRouter } from 'expo-router'
import { MapPin, Calendar, Clock } from 'lucide-react-native'
import colors from '@/constants/colors'
import { Task } from '@/types'
import Card from './Card'

interface TaskCardProps {
  task: Task
  testID?: string
}

export default function TaskCard({ task, testID }: TaskCardProps) {
  const router = useRouter()

  const handlePress = () => {
    router.push({ pathname: '/task/[id]', params: { id: task.id } })
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  return (
    <TouchableOpacity onPress={handlePress} testID={testID}>
      <Card>
        <View style={styles.header}>
          <Text style={styles.title}>{task.title}</Text>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: colors.statusColors[task.status] },
            ]}
          >
            <Text style={styles.statusText}>
              {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
            </Text>
          </View>
        </View>

        <Text style={styles.description} numberOfLines={2}>
          {task.description}
        </Text>

        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <MapPin size={16} color={colors.textLight} />
            <Text style={styles.detailText} numberOfLines={1}>
              {task.address}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Calendar size={16} color={colors.textLight} />
            <Text style={styles.detailText}>{formatDate(task.dueDate)}</Text>
          </View>

          <View style={styles.detailRow}>
            <Clock size={16} color={colors.textLight} />
            <Text style={styles.detailText}>{formatTime(task.dueDate)}</Text>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 12,
  },
  detailsContainer: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  detailText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 8,
  },
})
