import React, { ReactNode } from 'react'
import { StyleSheet, Text, View } from 'react-native'
import colors from '@/constants/colors'
import Card from './Card'

interface StatCardProps {
  title: string
  value: string | number
  icon?: ReactNode
  color?: string
  testID?: string
}

export default function StatCard({
  title,
  value,
  icon,
  color = colors.primary,
  testID,
}: StatCardProps) {
  return (
    <Card style={styles.card} testID={testID}>
      <View style={styles.container}>
        {icon && (
          <View
            style={[styles.iconContainer, { backgroundColor: `${color}20` }]}
          >
            {icon}
          </View>
        )}
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={[styles.value, { color }]}>{value}</Text>
        </View>
      </View>
    </Card>
  )
}

const styles = StyleSheet.create({
  card: {
    flex: 1,
    minWidth: '45%',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 4,
  },
  value: {
    fontSize: 20,
    fontWeight: '700',
  },
})
