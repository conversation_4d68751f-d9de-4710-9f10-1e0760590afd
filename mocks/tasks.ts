import { Task } from '@/types'

export const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Residential Collection - Oak Street',
    description: 'Weekly waste collection for residential area on Oak Street',
    address: '123 Oak Street, Springfield',
    status: 'pending',
    assignedTo: '1',
    dueDate: '2025-07-30T09:00:00Z',
    createdAt: '2025-07-25T14:30:00Z',
    location: {
      latitude: 37.7749,
      longitude: -122.4194,
    },
  },
  {
    id: '2',
    title: 'Commercial Collection - Downtown',
    description: 'Waste collection for downtown business district',
    address: '456 Main Street, Springfield',
    status: 'in-progress',
    assignedTo: '1',
    dueDate: '2025-07-29T14:00:00Z',
    createdAt: '2025-07-25T14:30:00Z',
    location: {
      latitude: 37.7833,
      longitude: -122.4167,
    },
  },
  {
    id: '3',
    title: 'Apartment Complex - Riverside',
    description: 'Waste collection for Riverside Apartments',
    address: '789 River Road, Springfield',
    status: 'completed',
    assignedTo: '1',
    dueDate: '2025-07-28T10:00:00Z',
    createdAt: '2025-07-25T14:30:00Z',
    completedAt: '2025-07-28T11:15:00Z',
    location: {
      latitude: 37.785,
      longitude: -122.4,
    },
    notes: 'All units collected, extra recycling from unit 12B',
    photos: [
      'https://images.unsplash.com/photo-1591955506264-3f5a6834570a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    ],
  },
  {
    id: '4',
    title: 'School District Collection',
    description: 'Waste collection for Springfield School District',
    address: '101 Education Lane, Springfield',
    status: 'pending',
    assignedTo: '1',
    dueDate: '2025-07-31T08:00:00Z',
    createdAt: '2025-07-25T14:30:00Z',
    location: {
      latitude: 37.79,
      longitude: -122.41,
    },
  },
  {
    id: '5',
    title: 'Park Cleanup - Central Park',
    description: 'Waste collection and cleanup at Central Park',
    address: 'Central Park, Springfield',
    status: 'pending',
    assignedTo: '1',
    dueDate: '2025-08-01T09:00:00Z',
    createdAt: '2025-07-25T14:30:00Z',
    location: {
      latitude: 37.77,
      longitude: -122.42,
    },
  },
]
