import { PaymentData } from '@/types'

// Public key can be used for client-side operations if needed
const PAYSTACK_PUBLIC_KEY = 'pk_test_4cae825d5090767ea02fbe15dbf6faa1ded293ee' // Replace with actual key
// Note: We'll call our own backend API instead of Paystack directly for security
const BACKEND_BASE_URL = 'https://your-backend-api.com' // Replace with your backend URL

export interface PaystackInitializeResponse {
  status: boolean
  message: string
  data: {
    authorization_url: string
    access_code: string
    reference: string
  }
}

export interface PaystackVerifyResponse {
  status: boolean
  message: string
  data: {
    id: number
    domain: string
    status: string
    reference: string
    amount: number
    message: string
    gateway_response: string
    paid_at: string
    created_at: string
    channel: string
    currency: string
    ip_address: string
    metadata: any
    log: any
    fees: number
    fees_split: any
    authorization: {
      authorization_code: string
      bin: string
      last4: string
      exp_month: string
      exp_year: string
      channel: string
      card_type: string
      bank: string
      country_code: string
      brand: string
      reusable: boolean
      signature: string
      account_name: string
    }
    customer: {
      id: number
      first_name: string
      last_name: string
      email: string
      customer_code: string
      phone: string
      metadata: any
      risk_action: string
      international_format_phone: string
    }
    plan: any
    split: any
    order_id: any
    paidAt: string
    createdAt: string
    requested_amount: number
    pos_transaction_data: any
    source: any
    fees_breakdown: any
  }
}

export class PaymentService {
  private static instance: PaymentService

  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService()
    }
    return PaymentService.instance
  }

  async initializeMobileMoneyPayment({
    amount,
    phoneNumber,
    email,
    reference,
    paymentMethod,
    customerName,
  }: {
    amount: number
    phoneNumber: string
    email: string
    reference: string
    paymentMethod: 'mtn_momo' | 'vodafone_cash' | 'airteltigo_money'
    customerName: string
  }): Promise<PaystackInitializeResponse> {
    try {
      // Call your backend API instead of Paystack directly
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/payments/initialize`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Add any authentication headers your backend requires
            // 'Authorization': `Bearer ${userToken}`,
          },
          body: JSON.stringify({
            amount,
            phoneNumber,
            email,
            reference,
            paymentMethod,
            customerName,
          }),
        }
      )

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `HTTP error! status: ${response.status}, message: ${
            errorData.message || 'Unknown error'
          }`
        )
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Payment initialization failed:', error)
      throw error
    }
  }

  async verifyPayment(reference: string): Promise<PaystackVerifyResponse> {
    try {
      // Call your backend API instead of Paystack directly
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/payments/verify/${reference}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            // Add any authentication headers your backend requires
            // 'Authorization': `Bearer ${userToken}`,
          },
        }
      )

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `HTTP error! status: ${response.status}, message: ${
            errorData.message || 'Unknown error'
          }`
        )
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Payment verification failed:', error)
      throw error
    }
  }

  private getPaystackChannel(paymentMethod: string): string {
    switch (paymentMethod) {
      case 'mtn_momo':
        return 'mobile_money'
      case 'vodafone_cash':
        return 'mobile_money'
      case 'airteltigo_money':
        return 'mobile_money'
      default:
        return 'mobile_money'
    }
  }

  private getPaystackProvider(paymentMethod: string): string {
    switch (paymentMethod) {
      case 'mtn_momo':
        return 'mtn'
      case 'vodafone_cash':
        return 'vod'
      case 'airteltigo_money':
        return 'tgo'
      default:
        return 'mtn'
    }
  }

  generatePaymentReference(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    return `CT_${timestamp}_${random}`.toUpperCase()
  }

  formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '')

    // Add Ghana country code if not present
    if (cleaned.length === 10 && cleaned.startsWith('0')) {
      cleaned = '233' + cleaned.substring(1)
    } else if (cleaned.length === 9) {
      cleaned = '233' + cleaned
    }

    return cleaned
  }

  validateGhanaPhoneNumber(phoneNumber: string): boolean {
    const formatted = this.formatPhoneNumber(phoneNumber)
    // Ghana phone numbers should be 12 digits starting with 233
    return /^233[0-9]{9}$/.test(formatted)
  }

  getPaymentMethodName(method: string): string {
    switch (method) {
      case 'mtn_momo':
        return 'MTN Mobile Money'
      case 'vodafone_cash':
        return 'Vodafone Cash'
      case 'airteltigo_money':
        return 'AirtelTigo Money'
      default:
        return 'Mobile Money'
    }
  }
}
