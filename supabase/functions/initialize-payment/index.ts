// Supabase Edge Function for initializing Paystack payments
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from '../_shared/cors.ts'

interface PaymentRequest {
  amount: number
  phoneNumber: string
  email: string
  reference: string
  paymentMethod: 'mtn_momo' | 'vodafone_cash' | 'airteltigo_money'
  customerName: string
}

function getPaystackChannel(paymentMethod: string): string {
  switch (paymentMethod) {
    case 'mtn_momo':
    case 'vodafone_cash':
    case 'airteltigo_money':
      return 'mobile_money'
    default:
      return 'mobile_money'
  }
}

function getPaystackProvider(paymentMethod: string): string {
  switch (paymentMethod) {
    case 'mtn_momo':
      return 'mtn'
    case 'vodafone_cash':
      return 'vod'
    case 'airteltigo_money':
      return 'tgo'
    default:
      return 'mtn'
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get Paystack secret key from environment variables
    const paystackSecretKey = Deno.env.get('PAYSTACK_SECRET_KEY')
    
    if (!paystackSecretKey) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: 'Paystack secret key not configured' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const body: PaymentRequest = await req.json()
    const { amount, phoneNumber, email, reference, paymentMethod, customerName } = body

    // Validate required fields
    if (!amount || !phoneNumber || !email || !reference || !paymentMethod || !customerName) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Missing required fields: amount, phoneNumber, email, reference, paymentMethod, customerName'
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Call Paystack API
    const paystackResponse = await fetch('https://api.paystack.co/transaction/initialize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paystackSecretKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: amount * 100, // Convert to pesewas (smallest unit)
        email,
        reference,
        currency: 'GHS',
        channels: [getPaystackChannel(paymentMethod)],
        metadata: {
          phone_number: phoneNumber,
          customer_name: customerName,
          payment_method: paymentMethod,
        },
        mobile_money: {
          phone: phoneNumber,
          provider: getPaystackProvider(paymentMethod),
        },
      }),
    })

    const paystackData = await paystackResponse.json()

    if (!paystackResponse.ok) {
      return new Response(
        JSON.stringify({
          success: false,
          message: paystackData.message || 'Payment initialization failed',
          error: paystackData
        }),
        { 
          status: paystackResponse.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Return successful response
    return new Response(
      JSON.stringify(paystackData),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Payment initialization error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Internal server error',
        error: error.message
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
