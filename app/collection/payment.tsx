import React, { useState, useEffect } from 'react'
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native'
import { useLocalSearchParams, useRouter } from 'expo-router'
import {
  CreditCard,
  Smartphone,
  CheckCircle,
  XCircle,
  Clock,
} from 'lucide-react-native'
import { useCollections } from '@/hooks/collections-store'
import { usePayments } from '@/hooks/payment-store'
import { useAuth } from '@/hooks/auth-store'
import Button from '@/components/Button'
import Card from '@/components/Card'
import colors from '@/constants/colors'
import { PaymentService } from '@/services/payment'
import { HouseholdInfo } from '@/types'

export default function PaymentCollectionScreen() {
  const { collectionId } = useLocalSearchParams<{ collectionId: string }>()
  const router = useRouter()
  const { collections, updateCollection } = useCollections()
  const { initializePayment, verifyPayment, getPaymentsByCollection } =
    usePayments()
  const { user } = useAuth()

  const collection = collections.find((c) => c.id === collectionId)
  const payments = getPaymentsByCollection(collectionId)
  const paymentService = PaymentService.getInstance()

  const [processingPayments, setProcessingPayments] = useState<Set<string>>(
    new Set()
  )
  const [verifyingPayments, setVerifyingPayments] = useState<Set<string>>(
    new Set()
  )

  useEffect(() => {
    // Auto-verify pending payments every 10 seconds
    const interval = setInterval(() => {
      payments
        .filter((p) => p.status === 'processing')
        .forEach((payment) => {
          handleVerifyPayment(payment.householdId)
        })
    }, 10000)

    return () => clearInterval(interval)
  }, [payments])

  if (!collection) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Collection not found</Text>
        <Button
          title="Go Back"
          onPress={() => router.back()}
          type="primary"
          style={styles.errorButton}
        />
      </View>
    )
  }

  const handleInitializePayment = async (household: HouseholdInfo) => {
    if (!user) {
      Alert.alert('Error', 'User data is missing')
      return
    }

    setProcessingPayments((prev) => new Set(prev).add(household.id))

    try {
      const payment = await initializePayment({
        collectionId: collection.id,
        householdId: household.id,
        amount: household.paymentAmount,
        phoneNumber: household.phoneNumber,
        paymentMethod: 'mtn_momo', // Default to MTN MoMo for now
        customerName: household.familyName,
      })

      if (payment) {
        // Update household payment status
        const updatedHouseholds = collection.households.map((h) =>
          h.id === household.id
            ? {
                ...h,
                paymentStatus: 'processing' as const,
                paymentReference: payment.reference,
              }
            : h
        )

        await updateCollection(collection.id, {
          households: updatedHouseholds,
          paymentStatus: getOverallPaymentStatus(updatedHouseholds),
        })

        Alert.alert(
          'Payment Initiated',
          `Payment request sent to ${household.phoneNumber}. Please ask the customer to complete the payment on their phone.`,
          [{ text: 'OK' }]
        )
      } else {
        throw new Error('Failed to initialize payment')
      }
    } catch (error) {
      console.error('Payment initialization failed:', error)
      Alert.alert('Error', 'Failed to initialize payment. Please try again.')
    } finally {
      setProcessingPayments((prev) => {
        const newSet = new Set(prev)
        newSet.delete(household.id)
        return newSet
      })
    }
  }

  const handleVerifyPayment = async (householdId: string) => {
    const payment = payments.find((p) => p.householdId === householdId)
    if (!payment) return

    setVerifyingPayments((prev) => new Set(prev).add(householdId))

    try {
      const isSuccessful = await verifyPayment(payment.id)

      // Update household payment status
      const updatedHouseholds = collection.households.map((h) =>
        h.id === householdId
          ? {
              ...h,
              paymentStatus: isSuccessful
                ? ('completed' as const)
                : ('failed' as const),
            }
          : h
      )

      await updateCollection(collection.id, {
        households: updatedHouseholds,
        paymentStatus: getOverallPaymentStatus(updatedHouseholds),
      })

      if (isSuccessful) {
        Alert.alert('Success', 'Payment completed successfully!')
      } else {
        Alert.alert(
          'Payment Failed',
          'Payment was not successful. Please try again.'
        )
      }
    } catch (error) {
      console.error('Payment verification failed:', error)
      Alert.alert('Error', 'Failed to verify payment status.')
    } finally {
      setVerifyingPayments((prev) => {
        const newSet = new Set(prev)
        newSet.delete(householdId)
        return newSet
      })
    }
  }

  const getOverallPaymentStatus = (households: HouseholdInfo[]) => {
    const completedCount = households.filter(
      (h) => h.paymentStatus === 'completed'
    ).length
    const totalCount = households.length

    if (completedCount === 0) return 'pending'
    if (completedCount === totalCount) return 'completed'
    return 'partial'
  }

  const getPaymentStatusIcon = (status: HouseholdInfo['paymentStatus']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={20} color={colors.success} />
      case 'failed':
        return <XCircle size={20} color={colors.error} />
      case 'processing':
        return <Clock size={20} color={colors.warning} />
      default:
        return <CreditCard size={20} color={colors.textLight} />
    }
  }

  const getPaymentStatusColor = (status: HouseholdInfo['paymentStatus']) => {
    switch (status) {
      case 'completed':
        return colors.success
      case 'failed':
        return colors.error
      case 'processing':
        return colors.warning
      default:
        return colors.textLight
    }
  }

  const completedPayments = collection.households.filter(
    (h) => h.paymentStatus === 'completed'
  ).length
  const totalAmount = collection.totalPaymentAmount
  const collectedAmount = collection.households
    .filter((h) => h.paymentStatus === 'completed')
    .reduce((sum, h) => sum + h.paymentAmount, 0)

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Payment Collection</Text>
        <Text style={styles.subtitle}>
          {collection.households.length} household(s) •{' '}
          {collection.totalWasteAmount} kg
        </Text>
      </View>

      <Card>
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Payment Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Amount:</Text>
            <Text style={styles.summaryValue}>
              GHS {totalAmount.toFixed(2)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Collected:</Text>
            <Text style={[styles.summaryValue, { color: colors.success }]}>
              GHS {collectedAmount.toFixed(2)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Remaining:</Text>
            <Text style={[styles.summaryValue, { color: colors.error }]}>
              GHS {(totalAmount - collectedAmount).toFixed(2)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Progress:</Text>
            <Text style={styles.summaryValue}>
              {completedPayments}/{collection.households.length} completed
            </Text>
          </View>
        </View>
      </Card>

      <Card>
        <Text style={styles.sectionTitle}>Households</Text>
        {collection.households.map((household, index) => {
          const payment = payments.find((p) => p.householdId === household.id)
          const isProcessing = processingPayments.has(household.id)
          const isVerifying = verifyingPayments.has(household.id)

          return (
            <View key={household.id} style={styles.householdCard}>
              <View style={styles.householdHeader}>
                <View style={styles.householdInfo}>
                  <Text style={styles.householdName}>
                    {index + 1}. {household.familyName}
                  </Text>
                  <Text style={styles.householdDetails}>
                    📱 {household.phoneNumber}
                  </Text>
                  <Text style={styles.householdDetails}>
                    {household.wasteAmount} kg • GHS{' '}
                    {household.paymentAmount.toFixed(2)}
                  </Text>
                </View>
                <View style={styles.paymentStatus}>
                  {getPaymentStatusIcon(household.paymentStatus)}
                  <Text
                    style={[
                      styles.paymentStatusText,
                      { color: getPaymentStatusColor(household.paymentStatus) },
                    ]}
                  >
                    {household.paymentStatus.charAt(0).toUpperCase() +
                      household.paymentStatus.slice(1)}
                  </Text>
                </View>
              </View>

              <View style={styles.householdActions}>
                {household.paymentStatus === 'pending' && (
                  <Button
                    title={isProcessing ? 'Initializing...' : 'Collect Payment'}
                    onPress={() => handleInitializePayment(household)}
                    type="primary"
                    loading={isProcessing}
                    style={styles.actionButton}
                    testID={`collect-payment-${household.id}`}
                  />
                )}

                {household.paymentStatus === 'processing' && (
                  <Button
                    title={isVerifying ? 'Verifying...' : 'Verify Payment'}
                    onPress={() => handleVerifyPayment(household.id)}
                    type="secondary"
                    loading={isVerifying}
                    style={styles.actionButton}
                    testID={`verify-payment-${household.id}`}
                  />
                )}

                {household.paymentStatus === 'failed' && (
                  <Button
                    title={isProcessing ? 'Retrying...' : 'Retry Payment'}
                    onPress={() => handleInitializePayment(household)}
                    type="outline"
                    loading={isProcessing}
                    style={styles.actionButton}
                    testID={`retry-payment-${household.id}`}
                  />
                )}

                {household.paymentStatus === 'completed' && (
                  <View style={styles.completedContainer}>
                    <CheckCircle size={16} color={colors.success} />
                    <Text style={styles.completedText}>Payment Completed</Text>
                  </View>
                )}
              </View>

              {payment && (
                <View style={styles.paymentInfo}>
                  <Text style={styles.paymentInfoText}>
                    Reference: {payment.reference}
                  </Text>
                  <Text style={styles.paymentInfoText}>
                    Method:{' '}
                    {paymentService.getPaymentMethodName(payment.paymentMethod)}
                  </Text>
                  <Text style={styles.paymentInfoText}>
                    Created: {new Date(payment.createdAt).toLocaleString()}
                  </Text>
                  {payment.completedAt && (
                    <Text style={styles.paymentInfoText}>
                      Completed:{' '}
                      {new Date(payment.completedAt).toLocaleString()}
                    </Text>
                  )}
                </View>
              )}
            </View>
          )
        })}
      </Card>

      <View style={styles.buttonsContainer}>
        <Button
          title="Done"
          onPress={() => router.back()}
          type="primary"
          style={styles.doneButton}
          testID="done-button"
        />
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 4,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    marginBottom: 16,
  },
  errorButton: {
    minWidth: 120,
  },
  summaryContainer: {
    marginVertical: 8,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 4,
  },
  summaryLabel: {
    fontSize: 16,
    color: colors.text,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  householdCard: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  householdHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  householdInfo: {
    flex: 1,
  },
  householdName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  householdDetails: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 2,
  },
  paymentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  paymentStatusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  householdActions: {
    marginBottom: 8,
  },
  actionButton: {
    marginBottom: 8,
  },
  completedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.success}10`,
    padding: 8,
    borderRadius: 6,
  },
  completedText: {
    fontSize: 14,
    color: colors.success,
    fontWeight: '500',
    marginLeft: 6,
  },
  paymentInfo: {
    backgroundColor: `${colors.primary}05`,
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
  },
  paymentInfoText: {
    fontSize: 12,
    color: colors.textLight,
    marginBottom: 2,
  },
  buttonsContainer: {
    padding: 16,
    marginBottom: 24,
  },
  doneButton: {
    marginBottom: 12,
  },
})
