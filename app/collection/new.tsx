import React, { useState, useEffect } from 'react'
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  Image,
} from 'react-native'
import { useLocalSearchParams, useRouter } from 'expo-router'
import {
  Camera,
  MapPin,
  Star,
  Plus,
  Trash2,
  Smartphone,
  CreditCard,
} from 'lucide-react-native'
import * as ImagePicker from 'expo-image-picker'
import * as Location from 'expo-location'
import { useCollections } from '@/hooks/collections-store'
import { useTasks } from '@/hooks/tasks-store'
import { useAuth } from '@/hooks/auth-store'
import { usePayments } from '@/hooks/payment-store'
import Input from '@/components/Input'
import Button from '@/components/Button'
import colors from '@/constants/colors'
import { CollectionData, HouseholdInfo } from '@/types'
import { PaymentService } from '@/services/payment'

export default function NewCollectionScreen() {
  const { taskId } = useLocalSearchParams<{ taskId: string }>()
  const router = useRouter()
  const { addCollection } = useCollections()
  const { getTaskById } = useTasks()
  const { user } = useAuth()
  const { initializePayment } = usePayments()

  const task = getTaskById(taskId)
  const paymentService = PaymentService.getInstance()

  const [households, setHouseholds] = useState<
    Omit<HouseholdInfo, 'id' | 'paymentStatus'>[]
  >([
    {
      familyName: '',
      phoneNumber: '',
      wasteAmount: 0,
      paymentAmount: 0,
    },
  ])
  const [wasteType, setWasteType] =
    useState<CollectionData['wasteType']>('general')
  const [householdRating, setHouseholdRating] =
    useState<CollectionData['householdRating']>(3)
  const [notes, setNotes] = useState('')
  const [photos, setPhotos] = useState<string[]>([])
  const [location, setLocation] = useState<{
    latitude: number
    longitude: number
  } | null>(null)
  const [locationError, setLocationError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<
    'mtn_momo' | 'vodafone_cash' | 'airteltigo_money'
  >('mtn_momo')
  const [pricePerKg, setPricePerKg] = useState('0.20') // Default price in GHS

  useEffect(() => {
    ;(async () => {
      if (Platform.OS !== 'web') {
        const { status } = await Location.requestForegroundPermissionsAsync()
        if (status !== 'granted') {
          setLocationError('Permission to access location was denied')
          return
        }
      }

      try {
        const currentLocation = await Location.getCurrentPositionAsync({})
        setLocation({
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
        })
      } catch (error) {
        console.error('Error getting location', error)
        setLocationError('Failed to get current location')

        // Fallback to task location if available
        if (task?.location) {
          setLocation(task.location)
          setLocationError('')
        }
      }
    })()
  }, [task])

  const pickImage = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Sorry, we need camera roll permissions to upload images.'
        )
        return
      }
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    })

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setPhotos([...photos, result.assets[0].uri])
    }
  }

  const addHousehold = () => {
    setHouseholds([
      ...households,
      {
        familyName: '',
        phoneNumber: '',
        wasteAmount: 0,
        paymentAmount: 0,
      },
    ])
  }

  const removeHousehold = (index: number) => {
    if (households.length > 1) {
      setHouseholds(households.filter((_, i) => i !== index))
    }
  }

  const updateHousehold = (
    index: number,
    field: keyof (typeof households)[0],
    value: string | number
  ) => {
    const updated = households.map((household, i) => {
      if (i === index) {
        const updatedHousehold = { ...household, [field]: value }
        // Auto-calculate payment amount based on waste amount and price per kg
        if (field === 'wasteAmount') {
          updatedHousehold.paymentAmount = Number(value) * Number(pricePerKg)
        }
        return updatedHousehold
      }
      return household
    })
    setHouseholds(updated)
  }

  const validateHouseholds = () => {
    for (let i = 0; i < households.length; i++) {
      const household = households[i]
      if (!household.familyName.trim()) {
        Alert.alert('Error', `Please enter family name for household ${i + 1}`)
        return false
      }
      if (!household.phoneNumber.trim()) {
        Alert.alert('Error', `Please enter phone number for household ${i + 1}`)
        return false
      }
      if (!paymentService.validateGhanaPhoneNumber(household.phoneNumber)) {
        Alert.alert(
          'Error',
          `Invalid phone number format for household ${
            i + 1
          }. Please use Ghana format (e.g., 0244123456)`
        )
        return false
      }
      if (household.wasteAmount <= 0) {
        Alert.alert(
          'Error',
          `Please enter a valid waste amount for household ${i + 1}`
        )
        return false
      }
    }
    return true
  }

  const handleSubmit = async () => {
    if (!validateHouseholds()) {
      return
    }

    if (!location) {
      Alert.alert('Error', 'Location data is required')
      return
    }

    if (!user) {
      Alert.alert('Error', 'User data is missing')
      return
    }

    setIsSubmitting(true)

    try {
      // Calculate totals
      const totalWasteAmount = households.reduce(
        (sum, h) => sum + h.wasteAmount,
        0
      )
      const totalPaymentAmount = households.reduce(
        (sum, h) => sum + h.paymentAmount,
        0
      )

      // Create household info with IDs and payment status
      const householdInfo: HouseholdInfo[] = households.map(
        (household, index) => ({
          id: `${Date.now()}_${index}`,
          familyName: household.familyName,
          phoneNumber: paymentService.formatPhoneNumber(household.phoneNumber),
          wasteAmount: household.wasteAmount,
          paymentAmount: household.paymentAmount,
          paymentStatus: 'pending',
        })
      )

      const newCollection: Omit<CollectionData, 'id'> = {
        taskId,
        collectedBy: user.id,
        timestamp: new Date().toISOString(),
        households: householdInfo,
        totalWasteAmount,
        totalPaymentAmount,
        wasteType,
        householdRating,
        notes: notes.trim() || undefined,
        photos: photos.length > 0 ? photos : undefined,
        location,
        paymentStatus: 'pending',
      }

      const savedCollection = await addCollection(newCollection)

      if (savedCollection) {
        // Show success and ask if user wants to collect payments now
        Alert.alert(
          'Collection Saved',
          `Collection data saved successfully. Total amount: GHS ${totalPaymentAmount.toFixed(
            2
          )}\n\nWould you like to collect payments now?`,
          [
            {
              text: 'Later',
              onPress: () => router.back(),
              style: 'cancel',
            },
            {
              text: 'Collect Payments',
              onPress: () =>
                router.push({
                  pathname: '/collection/payment',
                  params: { collectionId: savedCollection.id },
                }),
            },
          ]
        )
      } else {
        throw new Error('Failed to save collection')
      }
    } catch (error) {
      console.error('Failed to add collection', error)
      Alert.alert('Error', 'Failed to save collection data')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!task) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Task not found</Text>
        <Button
          title="Go Back"
          onPress={() => router.back()}
          type="primary"
          style={styles.errorButton}
        />
      </View>
    )
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Text style={styles.title}>New Collection</Text>
      <Text style={styles.subtitle}>{task.title}</Text>

      <View style={styles.formSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Payment Settings</Text>
        </View>

        <Input
          label="Price per kg (GHS)"
          placeholder="0.20"
          value={pricePerKg}
          onChangeText={setPricePerKg}
          keyboardType="numeric"
          testID="price-per-kg-input"
        />

        <Text style={styles.label}>Payment Method</Text>
        <View style={styles.paymentMethodContainer}>
          {(['mtn_momo', 'vodafone_cash', 'airteltigo_money'] as const).map(
            (method) => (
              <TouchableOpacity
                key={method}
                style={[
                  styles.paymentMethodButton,
                  paymentMethod === method && styles.paymentMethodButtonActive,
                ]}
                onPress={() => setPaymentMethod(method)}
                testID={`payment-method-${method}`}
              >
                <Smartphone
                  size={16}
                  color={
                    paymentMethod === method ? colors.primary : colors.textLight
                  }
                />
                <Text
                  style={[
                    styles.paymentMethodText,
                    paymentMethod === method && {
                      color: colors.primary,
                      fontWeight: '600',
                    },
                  ]}
                >
                  {paymentService.getPaymentMethodName(method)}
                </Text>
              </TouchableOpacity>
            )
          )}
        </View>
      </View>

      <View style={styles.formSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Households</Text>
          <TouchableOpacity onPress={addHousehold} style={styles.addButton}>
            <Plus size={20} color={colors.primary} />
            <Text style={styles.addButtonText}>Add Household</Text>
          </TouchableOpacity>
        </View>

        {households.map((household, index) => (
          <View key={index} style={styles.householdCard}>
            <View style={styles.householdHeader}>
              <Text style={styles.householdTitle}>Household {index + 1}</Text>
              {households.length > 1 && (
                <TouchableOpacity
                  onPress={() => removeHousehold(index)}
                  style={styles.removeButton}
                >
                  <Trash2 size={16} color={colors.error} />
                </TouchableOpacity>
              )}
            </View>

            <Input
              label="Family Name"
              placeholder="Enter family name"
              value={household.familyName}
              onChangeText={(value) =>
                updateHousehold(index, 'familyName', value)
              }
              testID={`family-name-${index}`}
            />

            <Input
              label="Phone Number"
              placeholder="0244123456"
              value={household.phoneNumber}
              onChangeText={(value) =>
                updateHousehold(index, 'phoneNumber', value)
              }
              keyboardType="phone-pad"
              testID={`phone-number-${index}`}
            />

            <Input
              label="Waste Amount (kg)"
              placeholder="Enter amount in kg"
              value={household.wasteAmount.toString()}
              onChangeText={(value) =>
                updateHousehold(index, 'wasteAmount', Number(value) || 0)
              }
              keyboardType="numeric"
              testID={`waste-amount-${index}`}
            />

            <View style={styles.paymentAmountContainer}>
              <CreditCard size={16} color={colors.success} />
              <Text style={styles.paymentAmountText}>
                Payment: GHS {household.paymentAmount.toFixed(2)}
              </Text>
            </View>
          </View>
        ))}

        <View style={styles.totalContainer}>
          <Text style={styles.totalText}>
            Total Waste: {households.reduce((sum, h) => sum + h.wasteAmount, 0)}{' '}
            kg
          </Text>
          <Text style={styles.totalText}>
            Total Payment: GHS{' '}
            {households.reduce((sum, h) => sum + h.paymentAmount, 0).toFixed(2)}
          </Text>
        </View>
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Waste Information</Text>

        <Text style={styles.label}>Waste Type</Text>
        <View style={styles.wasteTypeContainer}>
          {(['general', 'recyclable', 'organic', 'hazardous'] as const).map(
            (type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.wasteTypeButton,
                  wasteType === type && styles.wasteTypeButtonActive,
                  { borderColor: colors.wasteTypes[type] },
                ]}
                onPress={() => setWasteType(type)}
                testID={`waste-type-${type}`}
              >
                <View
                  style={[
                    styles.wasteTypeIndicator,
                    { backgroundColor: colors.wasteTypes[type] },
                  ]}
                />
                <Text
                  style={[
                    styles.wasteTypeText,
                    wasteType === type && { color: colors.wasteTypes[type] },
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            )
          )}
        </View>
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Household Rating</Text>
        <Text style={styles.ratingDescription}>
          Rate the household&apos;s waste management
        </Text>

        <View style={styles.ratingContainer}>
          {([1, 2, 3, 4, 5] as const).map((rating) => (
            <TouchableOpacity
              key={rating}
              style={[
                styles.ratingButton,
                householdRating === rating && styles.ratingButtonActive,
              ]}
              onPress={() => setHouseholdRating(rating)}
              testID={`rating-${rating}`}
            >
              <Star
                size={24}
                color={
                  householdRating >= rating ? colors.warning : colors.border
                }
                fill={
                  householdRating >= rating ? colors.warning : 'transparent'
                }
              />
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Location</Text>

        {locationError ? (
          <View style={styles.locationError}>
            <Text style={styles.locationErrorText}>{locationError}</Text>
          </View>
        ) : location ? (
          <View style={styles.locationContainer}>
            <MapPin size={20} color={colors.primary} />
            <Text style={styles.locationText}>
              Location captured: {location.latitude.toFixed(6)},{' '}
              {location.longitude.toFixed(6)}
            </Text>
          </View>
        ) : (
          <View style={styles.locationLoading}>
            <Text style={styles.locationLoadingText}>Getting location...</Text>
          </View>
        )}
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Additional Information</Text>

        <Input
          label="Notes (Optional)"
          placeholder="Enter any additional notes"
          value={notes}
          onChangeText={setNotes}
          multiline
          numberOfLines={4}
          style={styles.notesInput}
          testID="notes-input"
        />

        <Text style={styles.label}>Photos (Optional)</Text>
        <View style={styles.photosContainer}>
          {photos.map((photo, index) => (
            <Image
              key={index}
              source={{ uri: photo }}
              style={styles.photoThumbnail}
            />
          ))}

          <TouchableOpacity style={styles.addPhotoButton} onPress={pickImage}>
            <Camera size={24} color={colors.primary} />
            <Text style={styles.addPhotoText}>Add Photo</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.buttonsContainer}>
        <Button
          title="Submit"
          onPress={handleSubmit}
          loading={isSubmitting}
          style={styles.submitButton}
          testID="submit-button"
        />
        <Button
          title="Cancel"
          onPress={() => router.back()}
          type="outline"
          style={styles.cancelButton}
          testID="cancel-button"
        />
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    marginBottom: 16,
  },
  errorButton: {
    minWidth: 120,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textLight,
    marginBottom: 24,
  },
  formSection: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  wasteTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  wasteTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  wasteTypeButtonActive: {
    backgroundColor: `${colors.primary}10`,
  },
  wasteTypeIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  wasteTypeText: {
    fontSize: 14,
    color: colors.text,
  },
  ratingDescription: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  ratingButton: {
    padding: 8,
  },
  ratingButtonActive: {
    transform: [{ scale: 1.2 }],
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}10`,
    padding: 12,
    borderRadius: 8,
  },
  locationText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
    flex: 1,
  },
  locationError: {
    backgroundColor: `${colors.error}10`,
    padding: 12,
    borderRadius: 8,
  },
  locationErrorText: {
    fontSize: 14,
    color: colors.error,
  },
  locationLoading: {
    padding: 12,
    borderRadius: 8,
    backgroundColor: colors.border,
  },
  locationLoadingText: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
  },
  notesInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  photosContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  photoThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  addPhotoButton: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoText: {
    fontSize: 12,
    color: colors.primary,
    marginTop: 4,
  },
  buttonsContainer: {
    marginTop: 16,
  },
  submitButton: {
    marginBottom: 12,
  },
  cancelButton: {
    borderColor: colors.border,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: `${colors.primary}10`,
  },
  addButtonText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  paymentMethodContainer: {
    marginBottom: 16,
  },
  paymentMethodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 8,
  },
  paymentMethodButtonActive: {
    borderColor: colors.primary,
    backgroundColor: `${colors.primary}05`,
  },
  paymentMethodText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
  },
  householdCard: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  householdHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  householdTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  removeButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: `${colors.error}10`,
  },
  paymentAmountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.success}10`,
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
  },
  paymentAmountText: {
    fontSize: 14,
    color: colors.success,
    marginLeft: 6,
    fontWeight: '600',
  },
  totalContainer: {
    backgroundColor: `${colors.primary}10`,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  totalText: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: 4,
  },
})
