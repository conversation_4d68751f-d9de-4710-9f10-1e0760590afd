import React, { useState } from 'react'
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
} from 'react-native'
// import { useRouter } from 'expo-router';
import { Filter, Plus } from 'lucide-react-native'
import { useTasks } from '@/hooks/tasks-store'
import { useAuth } from '@/hooks/auth-store'
import TaskCard from '@/components/TaskCard'
import Button from '@/components/Button'
import colors from '@/constants/colors'
import { Task } from '@/types'

export default function TasksScreen() {
  // const router = useRouter();
  const { user, hasRole } = useAuth()
  const { tasks, isLoading } = useTasks()
  const [refreshing, setRefreshing] = useState(false)
  const [statusFilter, setStatusFilter] = useState<Task['status'] | 'all'>(
    'all'
  )

  const filteredTasks =
    statusFilter === 'all'
      ? tasks
      : tasks.filter((task) => task.status === statusFilter)

  const onRefresh = async () => {
    setRefreshing(true)
    // In a real app, this would refresh data from the server
    setTimeout(() => {
      setRefreshing(false)
    }, 1000)
  }

  const handleAddTask = () => {
    // In a real app, this would navigate to a task creation screen
    console.log('Add task')
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>Hello, {user?.name}</Text>
        <Text style={styles.subtitle}>
          {filteredTasks.length} {filteredTasks.length === 1 ? 'task' : 'tasks'}{' '}
          {statusFilter !== 'all' ? `(${statusFilter})` : ''}
        </Text>
      </View>

      <View style={styles.filterContainer}>
        <View style={styles.filterTabs}>
          <TouchableOpacity
            style={[
              styles.filterTab,
              statusFilter === 'all' && styles.activeFilterTab,
            ]}
            onPress={() => setStatusFilter('all')}
          >
            <Text
              style={[
                styles.filterText,
                statusFilter === 'all' && styles.activeFilterText,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterTab,
              statusFilter === 'pending' && styles.activeFilterTab,
            ]}
            onPress={() => setStatusFilter('pending')}
          >
            <Text
              style={[
                styles.filterText,
                statusFilter === 'pending' && styles.activeFilterText,
              ]}
            >
              Pending
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterTab,
              statusFilter === 'in-progress' && styles.activeFilterTab,
            ]}
            onPress={() => setStatusFilter('in-progress')}
          >
            <Text
              style={[
                styles.filterText,
                statusFilter === 'in-progress' && styles.activeFilterText,
              ]}
            >
              In Progress
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterTab,
              statusFilter === 'completed' && styles.activeFilterTab,
            ]}
            onPress={() => setStatusFilter('completed')}
          >
            <Text
              style={[
                styles.filterText,
                statusFilter === 'completed' && styles.activeFilterText,
              ]}
            >
              Completed
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      {filteredTasks.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No tasks found</Text>
          {hasRole(['manager', 'supervisor']) && (
            <Button
              title="Add New Task"
              onPress={handleAddTask}
              type="primary"
              style={styles.emptyButton}
            />
          )}
        </View>
      ) : (
        <FlatList
          data={filteredTasks}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TaskCard task={item} testID={`task-${item.id}`} />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      )}

      {hasRole(['manager', 'supervisor']) && (
        <TouchableOpacity style={styles.fab} onPress={handleAddTask}>
          <Plus size={24} color="white" />
        </TouchableOpacity>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 8,
    alignItems: 'center',
  },
  filterTabs: {
    flexDirection: 'row',
    flex: 1,
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 4,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeFilterTab: {
    backgroundColor: colors.primary,
  },
  filterText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textLight,
  },
  activeFilterText: {
    color: 'white',
  },
  filterButton: {
    marginLeft: 12,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textLight,
    marginBottom: 16,
  },
  emptyButton: {
    minWidth: 150,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
})
