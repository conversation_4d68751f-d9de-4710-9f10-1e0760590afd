import React from 'react'
import {
  StyleSheet,
  Text,
  View,
  ActivityIndicator,
  Platform,
} from 'react-native'
import { useTasks } from '@/hooks/tasks-store'
import colors from '@/constants/colors'

export default function MapScreen() {
  const { tasks, isLoading } = useTasks()
  // Unused state for now, will be used when implementing actual map functionality
  // const [mapError, setMapError] = useState<string | null>(null);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    )
  }

  // In a real app, we would use a map component like react-native-maps
  // For this demo, we'll just show a placeholder
  return (
    <View style={styles.container}>
      <View style={styles.mapPlaceholder}>
        <Text style={styles.mapPlaceholderText}>Map View</Text>
        <Text style={styles.mapPlaceholderSubtext}>
          {tasks.length} locations available
        </Text>

        {Platform.OS === 'web' ? (
          <Text style={styles.webNotice}>
            Map functionality is limited on web. For full features, please use
            the mobile app.
          </Text>
        ) : null}
      </View>

      <View style={styles.taskListContainer}>
        <Text style={styles.taskListTitle}>Nearby Tasks</Text>

        {tasks.slice(0, 3).map((task) => (
          <View key={task.id} style={styles.taskItem}>
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: colors.statusColors[task.status] },
              ]}
            />
            <View style={styles.taskDetails}>
              <Text style={styles.taskTitle}>{task.title}</Text>
              <Text style={styles.taskAddress}>{task.address}</Text>
            </View>
            <Text style={styles.taskDistance}>2.3 km</Text>
          </View>
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapPlaceholder: {
    height: '60%',
    backgroundColor: '#E8F4F8',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapPlaceholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
  },
  mapPlaceholderSubtext: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 8,
  },
  webNotice: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    padding: 12,
    borderRadius: 8,
    textAlign: 'center',
    color: colors.textLight,
  },
  taskListContainer: {
    flex: 1,
    padding: 16,
  },
  taskListTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  taskDetails: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  taskAddress: {
    fontSize: 14,
    color: colors.textLight,
    marginTop: 2,
  },
  taskDistance: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
  },
})
