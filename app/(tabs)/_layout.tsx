import { Tabs, useRouter } from 'expo-router'
import { useEffect } from 'react'
import { ClipboardList, Map, BarChart3, Settings } from 'lucide-react-native'
import { useAuth } from '@/hooks/auth-store'
import colors from '@/constants/colors'

export default function TabLayout() {
  const router = useRouter()
  const { user } = useAuth()

  useEffect(() => {
    // If no user is logged in, redirect to login
    if (!user) {
      router.replace('/(auth)/login')
    }
  }, [user, router])

  if (!user) {
    return null
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: '600',
        },
        tabBarStyle: {
          borderTopColor: colors.border,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Tasks',
          tabBarIcon: ({ color }) => <ClipboardList size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="map"
        options={{
          title: 'Map',
          tabBarIcon: ({ color }) => <Map size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="analytics"
        options={{
          title: 'Analytics',
          tabBarIcon: ({ color }) => <BarChart3 size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ color }) => <Settings size={24} color={color} />,
        }}
      />
    </Tabs>
  )
}
