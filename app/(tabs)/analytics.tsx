import React from 'react'
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  ActivityIndicator,
} from 'react-native'
import { BarChart3, Truck, Recycle, Scale, Star } from 'lucide-react-native'
import { useCollections } from '@/hooks/collections-store'
import StatCard from '@/components/StatCard'
import Card from '@/components/Card'
import colors from '@/constants/colors'
import { mockAnalytics } from '@/mocks/analytics'

export default function AnalyticsScreen() {
  const { collections, isLoading } = useCollections()

  // In a real app, we would calculate these from actual data
  // For this demo, we'll use mock data
  const analytics = mockAnalytics

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    )
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Text style={styles.title}>Performance Overview</Text>
      <Text style={styles.subtitle}>July 2025</Text>

      <View style={styles.statsGrid}>
        <StatCard
          title="Total Collections"
          value={analytics.totalCollections}
          icon={<Truck size={20} color={colors.primary} />}
          testID="total-collections-stat"
        />

        <StatCard
          title="Total Waste (kg)"
          value={analytics.totalWaste.toLocaleString()}
          icon={<Scale size={20} color={colors.secondary} />}
          color={colors.secondary}
          testID="total-waste-stat"
        />

        <StatCard
          title="Completion Rate"
          value={`${analytics.completionRate}%`}
          icon={<BarChart3 size={20} color={colors.info} />}
          color={colors.info}
          testID="completion-rate-stat"
        />

        <StatCard
          title="Average Rating"
          value={analytics.averageRating.toFixed(1)}
          icon={<Star size={20} color={colors.warning} />}
          color={colors.warning}
          testID="average-rating-stat"
        />
      </View>

      <Text style={styles.sectionTitle}>Waste Collection by Type</Text>

      <Card>
        <View style={styles.wasteTypeContainer}>
          {Object.entries(analytics.collectionsByType).map(([type, amount]) => (
            <View key={type} style={styles.wasteTypeItem}>
              <View style={styles.wasteTypeHeader}>
                <View
                  style={[
                    styles.wasteTypeIndicator,
                    {
                      backgroundColor:
                        colors.wasteTypes[
                          type as keyof typeof colors.wasteTypes
                        ],
                    },
                  ]}
                />
                <Text style={styles.wasteTypeTitle}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </View>
              <Text style={styles.wasteTypeAmount}>
                {amount.toLocaleString()} kg
              </Text>
              <View style={styles.wasteTypeBarContainer}>
                <View
                  style={[
                    styles.wasteTypeBar,
                    {
                      width: `${(amount / analytics.totalWaste) * 100}%`,
                      backgroundColor:
                        colors.wasteTypes[
                          type as keyof typeof colors.wasteTypes
                        ],
                    },
                  ]}
                />
              </View>
            </View>
          ))}
        </View>
      </Card>

      <Text style={styles.sectionTitle}>Recent Activity</Text>

      <Card>
        <View style={styles.activityContainer}>
          {collections.slice(0, 3).map((collection, index) => (
            <View
              key={collection.id}
              style={[
                styles.activityItem,
                index < collections.length - 1 && styles.activityItemBorder,
              ]}
            >
              <View
                style={[
                  styles.activityIcon,
                  {
                    backgroundColor: `${
                      colors.wasteTypes[collection.wasteType]
                    }20`,
                  },
                ]}
              >
                <Recycle
                  size={16}
                  color={colors.wasteTypes[collection.wasteType]}
                />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>
                  {collection.wasteType.charAt(0).toUpperCase() +
                    collection.wasteType.slice(1)}{' '}
                  Waste Collection
                </Text>
                <Text style={styles.activitySubtitle}>
                  {collection.wasteAmount} kg •{' '}
                  {new Date(collection.timestamp).toLocaleDateString()}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </Card>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textLight,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  wasteTypeContainer: {
    marginBottom: 8,
  },
  wasteTypeItem: {
    marginBottom: 16,
  },
  wasteTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  wasteTypeIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  wasteTypeTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  wasteTypeAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 6,
  },
  wasteTypeBarContainer: {
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  wasteTypeBar: {
    height: '100%',
    borderRadius: 4,
  },
  activityContainer: {
    marginVertical: 8,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  activityItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  activitySubtitle: {
    fontSize: 12,
    color: colors.textLight,
    marginTop: 2,
  },
})
