import React, { useState } from 'react'
import {
  StyleSheet,
  Text,
  View,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
} from 'react-native'
import { useRouter } from 'expo-router'
import { useAuth } from '@/hooks/auth-store'
import Input from '@/components/Input'
import Button from '@/components/Button'
import colors from '@/constants/colors'

export default function LoginScreen() {
  const router = useRouter()
  const { login, isLoading, error } = useAuth()

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [emailError, setEmailError] = useState('')
  const [passwordError, setPasswordError] = useState('')

  const validateInputs = () => {
    let isValid = true

    if (!email) {
      setEmailError('Email is required')
      isValid = false
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Please enter a valid email')
      isValid = false
    } else {
      setEmailError('')
    }

    if (!password) {
      setPasswordError('Password is required')
      isValid = false
    } else {
      setPasswordError('')
    }

    return isValid
  }

  const handleLogin = async () => {
    if (!validateInputs()) return

    const success = await login(email, password)
    if (success) {
      router.replace('/(tabs)')
    }
  }

  const handleDemoLogin = async (
    role: 'employee' | 'supervisor' | 'manager'
  ) => {
    let demoEmail = ''

    switch (role) {
      case 'employee':
        demoEmail = '<EMAIL>'
        break
      case 'supervisor':
        demoEmail = '<EMAIL>'
        break
      case 'manager':
        demoEmail = '<EMAIL>'
        break
    }

    const success = await login(demoEmail, 'password')
    if (success) {
      router.replace('/(tabs)')
    }
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.logoContainer}>
            <Image
              source={{
                uri: 'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              }}
              style={styles.logoBackground}
            />
            <View style={styles.overlay} />
            <View style={styles.logoContent}>
              <Text style={styles.appName}>CleanTrack</Text>
            </View>
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.title}>Sign In</Text>

            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            <Input
              label="Email"
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              error={emailError}
              testID="email-input"
            />

            <Input
              label="Password"
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              error={passwordError}
              testID="password-input"
            />

            <Button
              title="Sign In"
              onPress={handleLogin}
              loading={isLoading}
              style={styles.button}
              testID="login-button"
            />

            <View style={styles.demoContainer}>
              <Text style={styles.demoText}>Demo Accounts:</Text>
              <View style={styles.demoButtons}>
                <Button
                  title="Employee"
                  onPress={() => handleDemoLogin('employee')}
                  type="outline"
                  size="small"
                  style={styles.demoButton}
                  testID="demo-employee-button"
                />
                <Button
                  title="Supervisor"
                  onPress={() => handleDemoLogin('supervisor')}
                  type="outline"
                  size="small"
                  style={styles.demoButton}
                  testID="demo-supervisor-button"
                />
                <Button
                  title="Manager"
                  onPress={() => handleDemoLogin('manager')}
                  type="outline"
                  size="small"
                  style={styles.demoButton}
                  testID="demo-manager-button"
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    flexGrow: 1,
  },
  logoContainer: {
    height: 200,
    position: 'relative',
  },
  logoBackground: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 119, 182, 0.7)',
  },
  logoContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
  },
  formContainer: {
    flex: 1,
    padding: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 24,
  },
  errorContainer: {
    backgroundColor: `${colors.error}20`,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
  },
  button: {
    marginTop: 8,
  },
  demoContainer: {
    marginTop: 32,
  },
  demoText: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 12,
    textAlign: 'center',
  },
  demoButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  demoButton: {
    flex: 1,
    marginHorizontal: 4,
  },
})
