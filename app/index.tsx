import { useEffect } from 'react'
import { StyleSheet, Text, View, Image } from 'react-native'
import { useRouter } from 'expo-router'
import { useAuth } from '@/hooks/auth-store'
import Button from '@/components/Button'
import colors from '@/constants/colors'

export default function WelcomeScreen() {
  const router = useRouter()
  const { user } = useAuth()

  useEffect(() => {
    // If user is already logged in, redirect to the main app
    if (user) {
      router.replace('/(tabs)')
    }
  }, [user, router])

  const handleGetStarted = () => {
    router.push('/login')
  }

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <Image
          source={{
            uri: 'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          }}
          style={styles.logoBackground}
        />
        <View style={styles.overlay} />
        <View style={styles.logoContent}>
          <Text style={styles.appName}>CleanTrack</Text>
          <Text style={styles.tagline}>Smart Sanitation Field Management</Text>
        </View>
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title}>Streamline Your Sanitation Operations</Text>

        <View style={styles.featuresContainer}>
          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureIconText}>📊</Text>
            </View>
            <View style={styles.featureTextContainer}>
              <Text style={styles.featureTitle}>Real-time Data Collection</Text>
              <Text style={styles.featureDescription}>
                Capture sanitation data in the field with ease
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureIconText}>🚚</Text>
            </View>
            <View style={styles.featureTextContainer}>
              <Text style={styles.featureTitle}>Route Management</Text>
              <Text style={styles.featureDescription}>
                Optimize collection routes and track progress
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureIconText}>📱</Text>
            </View>
            <View style={styles.featureTextContainer}>
              <Text style={styles.featureTitle}>Mobile-First Design</Text>
              <Text style={styles.featureDescription}>
                Work efficiently from anywhere in the field
              </Text>
            </View>
          </View>
        </View>

        <Button
          title="Get Started"
          onPress={handleGetStarted}
          type="primary"
          size="large"
          style={styles.button}
          testID="get-started-button"
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  logoContainer: {
    height: '40%',
    position: 'relative',
  },
  logoBackground: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 119, 182, 0.7)',
  },
  logoContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  appName: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  contentContainer: {
    flex: 1,
    padding: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 24,
    textAlign: 'center',
  },
  featuresContainer: {
    marginBottom: 32,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${colors.primary}20`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureIconText: {
    fontSize: 24,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: colors.textLight,
  },
  button: {
    width: '100%',
  },
})
