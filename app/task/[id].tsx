import React, { useState } from 'react'
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native'
import { useLocalSearchParams, useRouter } from 'expo-router'
import {
  MapPin,
  Calendar,
  Clock,
  User,
  FileText,
  Plus,
} from 'lucide-react-native'
import { useTasks } from '@/hooks/tasks-store'
import { useCollections } from '@/hooks/collections-store'
import { useAuth } from '@/hooks/auth-store'
import Button from '@/components/Button'
import Card from '@/components/Card'
import colors from '@/constants/colors'

export default function TaskDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>()
  const router = useRouter()
  const { getTaskById, updateTask, isLoading: tasksLoading } = useTasks()
  const { getCollectionsByTask, isLoading: collectionsLoading } =
    useCollections()
  const { user } = useAuth()

  const [updating, setUpdating] = useState(false)

  const task = getTaskById(id)
  const collections = getCollectionsByTask(id)

  const isLoading = tasksLoading || collectionsLoading

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    )
  }

  if (!task) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Task not found</Text>
        <Button
          title="Go Back"
          onPress={() => router.back()}
          type="primary"
          style={styles.errorButton}
        />
      </View>
    )
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  const handleStartTask = async () => {
    setUpdating(true)
    try {
      await updateTask({
        ...task,
        status: 'in-progress',
      })
    } catch (error) {
      console.error('Failed to start task', error)
      Alert.alert('Error', 'Failed to start task')
    } finally {
      setUpdating(false)
    }
  }

  const handleCompleteTask = async () => {
    setUpdating(true)
    try {
      await updateTask({
        ...task,
        status: 'completed',
        completedAt: new Date().toISOString(),
      })
    } catch (error) {
      console.error('Failed to complete task', error)
      Alert.alert('Error', 'Failed to complete task')
    } finally {
      setUpdating(false)
    }
  }

  const handleCancelTask = async () => {
    Alert.alert('Cancel Task', 'Are you sure you want to cancel this task?', [
      {
        text: 'No',
        style: 'cancel',
      },
      {
        text: 'Yes',
        onPress: async () => {
          setUpdating(true)
          try {
            await updateTask({
              ...task,
              status: 'cancelled',
            })
          } catch (error) {
            console.error('Failed to cancel task', error)
            Alert.alert('Error', 'Failed to cancel task')
          } finally {
            setUpdating(false)
          }
        },
      },
    ])
  }

  const handleAddCollection = () => {
    router.push({
      pathname: '/collection/new',
      params: { taskId: task.id },
    })
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: colors.statusColors[task.status] },
          ]}
        >
          <Text style={styles.statusText}>
            {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
          </Text>
        </View>
        <Text style={styles.title}>{task.title}</Text>
        <Text style={styles.description}>{task.description}</Text>
      </View>

      <Card>
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <MapPin size={20} color={colors.primary} />
            <Text style={styles.detailText}>{task.address}</Text>
          </View>

          <View style={styles.detailRow}>
            <Calendar size={20} color={colors.primary} />
            <Text style={styles.detailText}>{formatDate(task.dueDate)}</Text>
          </View>

          <View style={styles.detailRow}>
            <Clock size={20} color={colors.primary} />
            <Text style={styles.detailText}>{formatTime(task.dueDate)}</Text>
          </View>

          <View style={styles.detailRow}>
            <User size={20} color={colors.primary} />
            <Text style={styles.detailText}>Assigned to: {user?.name}</Text>
          </View>

          {task.notes && (
            <View style={styles.detailRow}>
              <FileText size={20} color={colors.primary} />
              <Text style={styles.detailText}>{task.notes}</Text>
            </View>
          )}
        </View>
      </Card>

      {task.photos && task.photos.length > 0 && (
        <Card>
          <Text style={styles.sectionTitle}>Photos</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.photosContainer}
          >
            {task.photos.map((photo, index) => (
              <Image key={index} source={{ uri: photo }} style={styles.photo} />
            ))}
          </ScrollView>
        </Card>
      )}

      {collections.length > 0 && (
        <Card>
          <Text style={styles.sectionTitle}>Collections</Text>
          {collections.map((collection) => (
            <View key={collection.id} style={styles.collectionItem}>
              <View
                style={[
                  styles.collectionTypeIndicator,
                  { backgroundColor: colors.wasteTypes[collection.wasteType] },
                ]}
              />
              <View style={styles.collectionDetails}>
                <Text style={styles.collectionTitle}>
                  {collection.wasteType.charAt(0).toUpperCase() +
                    collection.wasteType.slice(1)}{' '}
                  Waste
                </Text>
                <Text style={styles.collectionSubtitle}>
                  {collection.totalWasteAmount} kg •{' '}
                  {collection.households.length} household(s) • Rating:{' '}
                  {collection.householdRating}/5
                </Text>
                <Text style={styles.collectionSubtitle}>
                  Total Payment: GHS {collection.totalPaymentAmount.toFixed(2)}{' '}
                  • Status: {collection.paymentStatus}
                </Text>
                <Text style={styles.collectionTimestamp}>
                  {new Date(collection.timestamp).toLocaleString()}
                </Text>

                {collection.households.map((household, index) => (
                  <View key={household.id} style={styles.householdItem}>
                    <Text style={styles.householdName}>
                      {index + 1}. {household.familyName}
                    </Text>
                    <Text style={styles.householdDetails}>
                      {household.wasteAmount} kg • GHS{' '}
                      {household.paymentAmount.toFixed(2)} •{' '}
                      {household.paymentStatus}
                    </Text>
                    <Text style={styles.householdPhone}>
                      📱 {household.phoneNumber}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          ))}
        </Card>
      )}

      <View style={styles.actionsContainer}>
        {task.status === 'pending' && (
          <Button
            title="Start Task"
            onPress={handleStartTask}
            type="primary"
            loading={updating}
            style={styles.actionButton}
            testID="start-task-button"
          />
        )}

        {task.status === 'in-progress' && (
          <>
            <Button
              title="Complete Task"
              onPress={handleCompleteTask}
              type="primary"
              loading={updating}
              style={styles.actionButton}
              testID="complete-task-button"
            />
            <Button
              title="Add Collection"
              onPress={handleAddCollection}
              type="secondary"
              style={[styles.actionButton, styles.secondaryButton]}
              testID="add-collection-button"
            />
          </>
        )}

        {(task.status === 'pending' || task.status === 'in-progress') && (
          <Button
            title="Cancel Task"
            onPress={handleCancelTask}
            type="outline"
            loading={updating}
            style={[styles.actionButton, styles.cancelButton]}
            textStyle={styles.cancelButtonText}
            testID="cancel-task-button"
          />
        )}
      </View>

      {task.status === 'in-progress' && (
        <TouchableOpacity
          style={styles.fab}
          onPress={handleAddCollection}
          testID="add-collection-fab"
        >
          <Plus size={24} color="white" />
        </TouchableOpacity>
      )}
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    marginBottom: 16,
  },
  errorButton: {
    minWidth: 120,
  },
  header: {
    padding: 16,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: colors.textLight,
    marginBottom: 16,
  },
  detailsContainer: {
    marginVertical: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  detailText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 12,
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  photosContainer: {
    flexDirection: 'row',
    marginVertical: 8,
  },
  photo: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginRight: 8,
  },
  collectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  collectionTypeIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  collectionDetails: {
    flex: 1,
  },
  collectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  collectionSubtitle: {
    fontSize: 14,
    color: colors.textLight,
    marginTop: 2,
  },
  collectionTimestamp: {
    fontSize: 12,
    color: colors.textLight,
    marginTop: 4,
  },
  actionsContainer: {
    padding: 16,
    marginBottom: 24,
  },
  actionButton: {
    marginBottom: 12,
  },
  secondaryButton: {
    backgroundColor: colors.secondary,
  },
  cancelButton: {
    borderColor: colors.error,
  },
  cancelButtonText: {
    color: colors.error,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  householdItem: {
    backgroundColor: colors.background,
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.primary,
  },
  householdName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  householdDetails: {
    fontSize: 12,
    color: colors.textLight,
    marginBottom: 2,
  },
  householdPhone: {
    fontSize: 12,
    color: colors.primary,
  },
})
