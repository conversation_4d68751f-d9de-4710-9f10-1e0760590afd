import AsyncStorage from '@react-native-async-storage/async-storage'
import createContextHook from '@nkzw/create-context-hook'
import { useEffect, useState } from 'react'
import { PaymentData } from '@/types'
import { PaymentService } from '@/services/payment'
import { useAuth } from './auth-store'

export const [PaymentContext, usePayments] = createContextHook(() => {
  const { user } = useAuth()
  const [payments, setPayments] = useState<PaymentData[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const paymentService = PaymentService.getInstance()

  useEffect(() => {
    const loadPayments = async () => {
      if (!user) return

      setIsLoading(true)
      try {
        const storedPayments = await AsyncStorage.getItem('payments')

        if (storedPayments) {
          setPayments(JSON.parse(storedPayments))
        } else {
          setPayments([])
        }
      } catch (err) {
        console.error('Failed to load payments', err)
        setError('Failed to load payments')
      } finally {
        setIsLoading(false)
      }
    }

    loadPayments()
  }, [user])

  const initializePayment = async ({
    collectionId,
    householdId,
    amount,
    phoneNumber,
    paymentMethod,
    customerName,
  }: {
    collectionId: string
    householdId: string
    amount: number
    phoneNumber: string
    paymentMethod: 'mtn_momo' | 'vodafone_cash' | 'airteltigo_money'
    customerName: string
  }): Promise<PaymentData | null> => {
    try {
      const reference = paymentService.generatePaymentReference()
      const formattedPhone = paymentService.formatPhoneNumber(phoneNumber)

      if (!paymentService.validateGhanaPhoneNumber(phoneNumber)) {
        throw new Error('Invalid Ghana phone number format')
      }

      // Create payment record
      const newPayment: PaymentData = {
        id: Date.now().toString(),
        collectionId,
        householdId,
        amount,
        currency: 'GHS',
        paymentMethod,
        phoneNumber: formattedPhone,
        reference,
        status: 'pending',
        createdAt: new Date().toISOString(),
      }

      // Initialize payment with Paystack
      const paystackResponse =
        await paymentService.initializeMobileMoneyPayment({
          amount,
          phoneNumber: formattedPhone,
          email: user?.email || '<EMAIL>',
          reference,
          paymentMethod,
          customerName,
        })

      if (paystackResponse.status) {
        newPayment.paystackReference = paystackResponse.data.reference
        newPayment.status = 'processing'
      }

      // Save payment record
      const updatedPayments = [...payments, newPayment]
      setPayments(updatedPayments)
      await AsyncStorage.setItem('payments', JSON.stringify(updatedPayments))

      return newPayment
    } catch (err) {
      console.error('Failed to initialize payment', err)
      setError(
        err instanceof Error ? err.message : 'Failed to initialize payment'
      )
      return null
    }
  }

  const verifyPayment = async (paymentId: string): Promise<boolean> => {
    try {
      const payment = payments.find((p) => p.id === paymentId)
      if (!payment || !payment.reference) {
        throw new Error('Payment not found')
      }

      const verificationResult = await paymentService.verifyPayment(
        payment.reference
      )

      if (
        verificationResult.status &&
        verificationResult.data.status === 'success'
      ) {
        // Update payment status
        const updatedPayments = payments.map((p) =>
          p.id === paymentId
            ? {
                ...p,
                status: 'completed' as const,
                completedAt: new Date().toISOString(),
              }
            : p
        )

        setPayments(updatedPayments)
        await AsyncStorage.setItem('payments', JSON.stringify(updatedPayments))
        return true
      } else {
        // Update payment as failed
        const updatedPayments = payments.map((p) =>
          p.id === paymentId
            ? {
                ...p,
                status: 'failed' as const,
                failureReason:
                  verificationResult.message || 'Payment verification failed',
              }
            : p
        )

        setPayments(updatedPayments)
        await AsyncStorage.setItem('payments', JSON.stringify(updatedPayments))
        return false
      }
    } catch (err) {
      console.error('Failed to verify payment', err)
      setError(err instanceof Error ? err.message : 'Failed to verify payment')
      return false
    }
  }

  const getPaymentsByCollection = (collectionId: string) => {
    return payments.filter((payment) => payment.collectionId === collectionId)
  }

  const getPaymentsByHousehold = (householdId: string) => {
    return payments.filter((payment) => payment.householdId === householdId)
  }

  const updatePaymentStatus = async (
    paymentId: string,
    status: PaymentData['status'],
    failureReason?: string
  ) => {
    try {
      const updatedPayments = payments.map((p) =>
        p.id === paymentId
          ? {
              ...p,
              status,
              ...(status === 'completed' && {
                completedAt: new Date().toISOString(),
              }),
              ...(failureReason && { failureReason }),
            }
          : p
      )

      setPayments(updatedPayments)
      await AsyncStorage.setItem('payments', JSON.stringify(updatedPayments))
    } catch (err) {
      console.error('Failed to update payment status', err)
      setError('Failed to update payment status')
    }
  }

  return {
    payments,
    isLoading,
    error,
    initializePayment,
    verifyPayment,
    getPaymentsByCollection,
    getPaymentsByHousehold,
    updatePaymentStatus,
  }
})
