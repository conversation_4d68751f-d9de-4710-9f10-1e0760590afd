import AsyncStorage from '@react-native-async-storage/async-storage'
import createContextHook from '@nkzw/create-context-hook'
import { useEffect, useState } from 'react'
import { CollectionData } from '@/types'
import { mockCollections } from '@/mocks/collections'
import { useAuth } from './auth-store'

export const [CollectionsContext, useCollections] = createContextHook(() => {
  const { user } = useAuth()
  const [collections, setCollections] = useState<CollectionData[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadCollections = async () => {
      if (!user) return

      setIsLoading(true)
      try {
        const storedCollections = await AsyncStorage.getItem('collections')

        if (storedCollections) {
          setCollections(JSON.parse(storedCollections))
        } else {
          // Initialize with mock data
          setCollections(mockCollections)
          await AsyncStorage.setItem(
            'collections',
            JSON.stringify(mockCollections)
          )
        }
      } catch (err) {
        console.error('Failed to load collections', err)
        setError('Failed to load collections')
      } finally {
        setIsLoading(false)
      }
    }

    loadCollections()
  }, [user])

  const addCollection = async (newCollection: Omit<CollectionData, 'id'>) => {
    try {
      const collectionToAdd: CollectionData = {
        ...newCollection,
        id: Date.now().toString(),
      }

      const updatedCollections = [...collections, collectionToAdd]
      setCollections(updatedCollections)
      await AsyncStorage.setItem(
        'collections',
        JSON.stringify(updatedCollections)
      )
      return collectionToAdd
    } catch (err) {
      console.error('Failed to add collection', err)
      setError('Failed to add collection')
      return null
    }
  }

  const updateCollection = async (
    collectionId: string,
    updates: Partial<CollectionData>
  ) => {
    try {
      const updatedCollections = collections.map((collection) =>
        collection.id === collectionId
          ? { ...collection, ...updates }
          : collection
      )

      setCollections(updatedCollections)
      await AsyncStorage.setItem(
        'collections',
        JSON.stringify(updatedCollections)
      )
      return true
    } catch (err) {
      console.error('Failed to update collection', err)
      setError('Failed to update collection')
      return false
    }
  }

  const getCollectionsByTask = (taskId: string) => {
    return collections.filter((collection) => collection.taskId === taskId)
  }

  const getCollectionsByEmployee = (employeeId: string) => {
    return collections.filter(
      (collection) => collection.collectedBy === employeeId
    )
  }

  const getTotalCollectionStats = () => {
    const totalWaste = collections.reduce(
      (sum, collection) => sum + collection.totalWasteAmount,
      0
    )
    const totalCollections = collections.length
    const totalPayments = collections.reduce(
      (sum, collection) => sum + collection.totalPaymentAmount,
      0
    )

    const wasteByType = collections.reduce((acc, collection) => {
      acc[collection.wasteType] =
        (acc[collection.wasteType] || 0) + collection.totalWasteAmount
      return acc
    }, {} as Record<string, number>)

    const averageRating =
      collections.length > 0
        ? collections.reduce(
            (sum, collection) => sum + collection.householdRating,
            0
          ) / collections.length
        : 0

    const paymentStatusStats = collections.reduce((acc, collection) => {
      acc[collection.paymentStatus] = (acc[collection.paymentStatus] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      totalWaste,
      totalCollections,
      totalPayments,
      wasteByType,
      averageRating,
      paymentStatusStats,
    }
  }

  return {
    collections,
    isLoading,
    error,
    addCollection,
    updateCollection,
    getCollectionsByTask,
    getCollectionsByEmployee,
    getTotalCollectionStats,
  }
})
