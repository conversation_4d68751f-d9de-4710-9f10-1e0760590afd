import AsyncStorage from '@react-native-async-storage/async-storage'
import createContextHook from '@nkzw/create-context-hook'
import { useEffect, useState } from 'react'
import { Task } from '@/types'
import { mockTasks } from '@/mocks/tasks'
import { useAuth } from './auth-store'

export const [TasksContext, useTasks] = createContextHook(() => {
  const { user } = useAuth()
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadTasks = async () => {
      if (!user) return

      setIsLoading(true)
      try {
        const storedTasks = await AsyncStorage.getItem('tasks')

        if (storedTasks) {
          setTasks(JSON.parse(storedTasks))
        } else {
          // Initialize with mock data
          const initialTasks =
            user.role === 'employee'
              ? mockTasks.filter((task) => task.assignedTo === user.id)
              : mockTasks

          setTasks(initialTasks)
          await AsyncStorage.setItem('tasks', JSON.stringify(initialTasks))
        }
      } catch (err) {
        console.error('Failed to load tasks', err)
        setError('Failed to load tasks')
      } finally {
        setIsLoading(false)
      }
    }

    loadTasks()
  }, [user])

  const getTaskById = (id: string): Task | undefined => {
    return tasks.find((task) => task.id === id)
  }

  const updateTask = async (updatedTask: Task) => {
    try {
      const updatedTasks = tasks.map((task) =>
        task.id === updatedTask.id ? updatedTask : task
      )

      setTasks(updatedTasks)
      await AsyncStorage.setItem('tasks', JSON.stringify(updatedTasks))
      return true
    } catch (err) {
      console.error('Failed to update task', err)
      setError('Failed to update task')
      return false
    }
  }

  const addTask = async (newTask: Omit<Task, 'id' | 'createdAt'>) => {
    try {
      const taskToAdd: Task = {
        ...newTask,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
      }

      const updatedTasks = [...tasks, taskToAdd]
      setTasks(updatedTasks)
      await AsyncStorage.setItem('tasks', JSON.stringify(updatedTasks))
      return taskToAdd
    } catch (err) {
      console.error('Failed to add task', err)
      setError('Failed to add task')
      return null
    }
  }

  const getTasksByStatus = (status: Task['status']) => {
    return tasks.filter((task) => task.status === status)
  }

  const getTasksByAssignee = (assigneeId: string) => {
    return tasks.filter((task) => task.assignedTo === assigneeId)
  }

  return {
    tasks,
    isLoading,
    error,
    getTaskById,
    updateTask,
    addTask,
    getTasksByStatus,
    getTasksByAssignee,
  }
})
