import AsyncStorage from '@react-native-async-storage/async-storage'
import create<PERSON>ontextHook from '@nkzw/create-context-hook'
import { useEffect, useState } from 'react'
import { User, UserRole } from '@/types'
import { mockUsers } from '@/mocks/users'

export const [AuthContext, useAuth] = createContextHook(() => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadUser = async () => {
      try {
        const storedUser = await AsyncStorage.getItem('user')
        if (storedUser) {
          setUser(JSON.parse(storedUser))
        }
      } catch (err) {
        console.error('Failed to load user from storage', err)
        setError('Failed to load user data')
      } finally {
        setIsLoading(false)
      }
    }

    loadUser()
  }, [])

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call with mock data
      const foundUser = mockUsers.find((u) => u.email === email)

      if (foundUser && password === 'password') {
        // Simple password check for demo
        setUser(foundUser)
        await AsyncStorage.setItem('user', JSON.stringify(foundUser))
        return true
      } else {
        setError('Invalid email or password')
        return false
      }
    } catch (err) {
      console.error('Login error', err)
      setError('An error occurred during login')
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      await AsyncStorage.removeItem('user')
      setUser(null)
    } catch (err) {
      console.error('Logout error', err)
      setError('An error occurred during logout')
    } finally {
      setIsLoading(false)
    }
  }

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!user) return false

    if (Array.isArray(roles)) {
      return roles.includes(user.role)
    }

    return user.role === roles
  }

  return {
    user,
    isLoading,
    error,
    login,
    logout,
    hasRole,
  }
})
