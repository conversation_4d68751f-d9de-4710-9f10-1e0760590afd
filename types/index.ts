export type UserRole = 'employee' | 'supervisor' | 'manager'

export interface User {
  id: string
  name: string
  email: string
  role: UserRole
  avatar?: string
}

export interface Task {
  id: string
  title: string
  description: string
  address: string
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled'
  assignedTo: string
  dueDate: string
  createdAt: string
  completedAt?: string
  location?: {
    latitude: number
    longitude: number
  }
  notes?: string
  photos?: string[]
}

export interface HouseholdInfo {
  id: string
  familyName: string
  phoneNumber: string
  wasteAmount: number // in kg
  paymentAmount: number // in GHS
  paymentStatus: 'pending' | 'processing' | 'completed' | 'failed'
  paymentReference?: string
  paymentMethod?: 'mtn_momo' | 'vodafone_cash' | 'airteltigo_money'
}

export interface PaymentData {
  id: string
  collectionId: string
  householdId: string
  amount: number
  currency: 'GHS'
  paymentMethod: 'mtn_momo' | 'vodafone_cash' | 'airteltigo_money'
  phoneNumber: string
  reference: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  paystackReference?: string
  createdAt: string
  completedAt?: string
  failureReason?: string
}

export interface CollectionData {
  id: string
  taskId: string
  collectedBy: string
  timestamp: string
  households: HouseholdInfo[]
  totalWasteAmount: number // in kg
  totalPaymentAmount: number // in GHS
  wasteType: 'general' | 'recyclable' | 'organic' | 'hazardous'
  householdRating: 1 | 2 | 3 | 4 | 5
  notes?: string
  photos?: string[]
  location: {
    latitude: number
    longitude: number
  }
  paymentStatus: 'pending' | 'partial' | 'completed'
}

export interface AnalyticsData {
  totalCollections: number
  totalWaste: number
  completionRate: number
  averageRating: number
  collectionsByType: {
    general: number
    recyclable: number
    organic: number
    hazardous: number
  }
}
